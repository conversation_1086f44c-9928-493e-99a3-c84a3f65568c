"use client"

import { <PERSON><PERSON>, <PERSON>si<PERSON>, type NodeProps } from "@xyflow/react"
import {
    Brain,
    Code,
    Database,
    GitBranch,
    Globe,
    MessageSquare,
    Play,
    RotateCcw,
    Route,
    User,
    Workflow
} from "lucide-react"
import { useEditorStore } from "../../stores/editorStore"
import { NodeActionBar } from "./NodeActionBar"

const iconMap = {
  agent: User,
  api: Globe,
  condition: GitBranch,
  function: Code,
  router: Route,
  memory: Database,
  knowledge: Brain,
  workflow: Workflow,
  response: MessageSquare,
  loop: RotateCcw,
  start: Play,
}

const colorMap = {
  agent: "bg-primary-500",
  api: "bg-accent-500",
  condition: "bg-orange-500",
  function: "bg-secondary-500",
  router: "bg-green-500",
  memory: "bg-pink-500",
  knowledge: "bg-teal-500",
  workflow: "bg-amber-500",
  response: "bg-blue-500",
  loop: "bg-cyan-500",
  start: "bg-neutral-600",
}

interface CustomNodeData {
  label: string
  type: string
  description?: string
}

export function CustomNode({ data, selected, id }: NodeProps) {
  const highlightedNodes = useEditorStore((state) => state.highlightedNodes)
  const isHighlighted = highlightedNodes.includes(id)

  // Get node action functions from the store
  const {
    toggleNodeEnabled,
    duplicateNode,
    toggleNodePorts,
    deleteNode,
    openNodeSettings,
  } = useEditorStore()

  const nodeData = data as CustomNodeData
  const Icon = iconMap[nodeData.type as keyof typeof iconMap] || Play
  const bgColor = colorMap[nodeData.type as keyof typeof colorMap] || "bg-neutral-600"

  // Get node state
  const isEnabled = nodeData.enabled ?? true
  const hasVerticalPorts = nodeData.hasVerticalPorts ?? false

  // Determine the border and shadow styles based on selection and highlighting
  const getBorderStyles = () => {
    if (selected) {
      return "border-primary-500 shadow-lg shadow-primary-500/20"
    }
    if (isHighlighted) {
      return "border-teal-400 shadow-lg shadow-teal-400/25 ring-2 ring-teal-400/15"
    }
    return "border-border hover:border-primary-500/50 hover:shadow-md"
  }

  return (
    <div className={`group relative px-4 py-3 rounded-md bg-background border-2 min-w-[240px] transition-all duration-300 ${getBorderStyles()} ${!isEnabled ? 'opacity-60' : ''}`}>
      {/* Node Action Bar */}
      <NodeActionBar
        nodeId={id}
        nodeType={nodeData.type}
        isEnabled={isEnabled}
        hasVerticalPorts={hasVerticalPorts}
        onToggleEnabled={toggleNodeEnabled}
        onDuplicate={duplicateNode}
        onTogglePorts={toggleNodePorts}
        onDelete={deleteNode}
        onSettings={openNodeSettings}
      />

      <Handle
        type="target"
        position={hasVerticalPorts ? Position.Top : Position.Left}
        className={`${hasVerticalPorts ? '!w-5 !h-[7px] !top-[-7px]' : '!w-[7px] !h-5 !left-[-7px]'} !bg-blue-400 dark:!bg-blue-500 !rounded-[2px] !border-none !z-[30] group-hover:!shadow-[0_0_0_3px_rgba(59,130,246,0.15)] ${hasVerticalPorts ? 'hover:!h-[10px] hover:!top-[-10px] hover:!rounded-t-full hover:!rounded-b-none' : 'hover:!w-[10px] hover:!left-[-10px] hover:!rounded-l-full hover:!rounded-r-none'} hover:!bg-blue-500 dark:hover:!bg-blue-400 !cursor-crosshair transition-[colors] duration-150`}
        style={{
          ...(hasVerticalPorts
            ? { left: '50%', transform: 'translateX(-50%)' }
            : { top: '50%', transform: 'translateY(-50%)' }),
        }}
      />

      <div className="flex items-center space-x-3">
        <div className={`w-10 h-10 ${bgColor} rounded-md flex items-center justify-center flex-shrink-0 shadow-sm ${!isEnabled ? 'grayscale' : ''}`}>
          <Icon className="w-5 h-5 text-white" />
        </div>

        <div className="flex-1 min-w-0">
          <div className="text-sm font-semibold text-foreground truncate">
            {nodeData.label}
          </div>
          {nodeData.description && (
            <div className="text-xs text-muted-foreground truncate mt-0.5">
              {nodeData.description}
            </div>
          )}
        </div>
      </div>

      <Handle
        type="source"
        position={hasVerticalPorts ? Position.Bottom : Position.Right}
        className={`${hasVerticalPorts ? '!w-5 !h-[7px] !bottom-[-7px]' : '!w-[7px] !h-5 !right-[-7px]'} !bg-green-400 dark:!bg-green-500 !rounded-[2px] !border-none !z-[30] group-hover:!shadow-[0_0_0_3px_rgba(34,197,94,0.15)] ${hasVerticalPorts ? 'hover:!h-[10px] hover:!bottom-[-10px] hover:!rounded-b-full hover:!rounded-t-none' : 'hover:!w-[10px] hover:!right-[-10px] hover:!rounded-r-full hover:!rounded-l-none'} hover:!bg-green-500 dark:hover:!bg-green-400 !cursor-crosshair transition-[colors] duration-150`}
        style={{
          ...(hasVerticalPorts
            ? { left: '50%', transform: 'translateX(-50%)' }
            : { top: '50%', transform: 'translateY(-50%)' }),
        }}
      />
    </div>
  )
}
